/* 浮動按鈕群組樣式 */
.floating-buttons {
    position: fixed;
    bottom: 30px;
    right: 20px;
    display: flex;
    flex-direction: column;
    gap: 15px;
    z-index: 1000;
    pointer-events: none; /* 讓容器本身不阻擋點擊 */
}

.floating-buttons > * {
    pointer-events: auto; /* 恢復按鈕的點擊功能 */
}

.float-button {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #d35400, #e67e22);
    border-radius: 50%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    text-decoration: none;
    border: none;
    position: relative;
    overflow: hidden;
    font-family: inherit;
}

.float-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
    transition: left 0.5s ease;
}

.float-button:hover {
    transform: scale(1.1) rotate(5deg);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
}

.float-button:hover::before {
    left: 100%;
}

.float-button:active {
    transform: scale(0.95);
}

/* 回到頂部按鈕 */
.back-to-top {
    background: linear-gradient(135deg, #27ae60, #2ecc71);
    opacity: 1;
    visibility: visible;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    transform: translateY(0);
}

.back-to-top.visible {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

/* 訂單查詢按鈕 */
.order-query {
    background: linear-gradient(135deg, #FF9800, #F57C00);
}

/* Facebook 按鈕 */
.facebook {
    background: linear-gradient(135deg, #1877F2, #166FE5);
}

/* LINE 按鈕 */
.line {
    background: linear-gradient(135deg, #00B900, #00A000);
}

/* 線上訂購按鈕 */
.order {
    background: linear-gradient(135deg, #e53e3e, #c53030);
}

.float-button svg {
    width: 30px;
    height: 30px;
    margin-bottom: 2px;
    filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.2));
    flex-shrink: 0;
}

.float-button span {
    font-size: 11px;
    text-align: center;
    font-weight: 700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    letter-spacing: 0.3px;
    line-height: 1;
    margin-top: 1px;
}

/* 響應式設計 */
@media screen and (max-width: 768px) {
    .floating-buttons {
        right: 15px;
        bottom: 20px;
        gap: 12px;
    }

    .float-button {
        width: 55px;
        height: 55px;
    }

    .float-button svg {
        width: 26px;
        height: 26px;
        margin-bottom: 2px;
    }

    .float-button span {
        font-size: 10px;
    }
}

@media screen and (max-width: 480px) {
    .floating-buttons {
        right: 10px;
        bottom: 15px;
        gap: 10px;
    }

    .float-button {
        width: 50px;
        height: 50px;
    }

    .float-button svg {
        width: 24px;
        height: 24px;
        margin-bottom: 1px;
    }

    .float-button span {
        font-size: 9px;
    }
}

/* 動畫效果 */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-5px);
    }
}

.float-button:not(:hover) {
    animation: float 3s ease-in-out infinite;
}

.float-button:nth-child(1) {
    animation-delay: 0s;
}

.float-button:nth-child(2) {
    animation-delay: 0.5s;
}

.float-button:nth-child(3) {
    animation-delay: 1s;
}

.float-button:nth-child(4) {
    animation-delay: 1.5s;
}

.float-button:nth-child(5) {
    animation-delay: 2s;
}

/* 特殊效果 */
.float-button::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.3s ease;
}

.float-button:active::after {
    width: 100%;
    height: 100%;
}

/* 無障礙設計 */
.float-button:focus {
    outline: 2px solid #fff;
    outline-offset: 2px;
}

.float-button:focus:not(:focus-visible) {
    outline: none;
}

/* 高對比模式支援 */
@media (prefers-contrast: high) {
    .float-button {
        border: 2px solid #fff;
    }
}

/* 減少動畫偏好設定 */
@media (prefers-reduced-motion: reduce) {
    .float-button {
        animation: none;
        transition: none;
    }

    .float-button:hover {
        transform: scale(1.05);
    }
}
