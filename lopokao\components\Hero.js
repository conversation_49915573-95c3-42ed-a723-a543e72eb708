function Hero() {
    try {
        return (
            <section className="pt-20 bg-gray-50" data-name="hero-section">
                <div className="container mx-auto px-4 py-16">
                    <div className="flex flex-col md:flex-row items-center" data-name="hero-content">
                        <div className="md:w-1/2 mb-8 md:mb-0" data-name="hero-text">
                            <h2 className="text-4xl font-bold mb-4 text-gray-800" data-name="hero-title">
                                傳統美味，用心製作
                            </h2>
                            <p className="text-lg text-gray-600 mb-6" data-name="hero-description">
                                堅持使用在地新鮮食材，純手工製作，每一口都能品嚐到傳統的滋味。
                                我們用心維持傳統工藝，為您帶來最道地的美食體驗。
                            </p>
                            <a 
                                href="#order" 
                                className="btn btn-primary"
                                data-name="hero-cta"
                            >
                                立即訂購
                            </a>
                        </div>
                        <div className="md:w-1/2" data-name="hero-image">
                            <img 
                                src="https://app.trickle.so/storage/public/images/usr_0f3710f6b8000001/730fd269-5ab3-4072-b371-5765e252731d.jpeg"
                                alt="手工蘿蔔糕展示"
                                className="rounded-lg shadow-lg"
                            />
                        </div>
                    </div>
                </div>
            </section>
        );
    } catch (error) {
        console.error('Hero component error:', error);
        reportError(error);
        return null;
    }
}
