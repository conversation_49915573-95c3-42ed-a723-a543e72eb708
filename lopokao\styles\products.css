.products-section {
    padding: 4rem 0;
    background-color: #f7fafc;
}

.product-grid {
    display: grid;
    gap: 2rem;
    padding: 1rem;
}

.product-card {
    background-color: #ffffff;
    border-radius: 0.5rem;
    overflow: hidden;
    transition: transform 0.3s ease;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.product-card:hover {
    transform: translateY(-4px);
}

.product-image {
    width: 100%;
    height: 240px;
    object-fit: cover;
}

.product-info {
    padding: 1.5rem;
}

.product-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: #2d3748;
    margin-bottom: 0.5rem;
}

.product-description {
    color: #718096;
    margin-bottom: 1rem;
}

.product-price {
    color: #e53e3e;
    font-size: 1.5rem;
    font-weight: 700;
}

@media (min-width: 768px) {
    .product-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .product-grid {
        grid-template-columns: repeat(3, 1fr);
    }
}
