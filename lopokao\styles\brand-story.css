/* 品牌故事區塊樣式 */
.brand-story-section {
    position: relative;
    overflow: hidden;
}

.brand-story-section::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: 
        radial-gradient(circle at 20% 20%, rgba(251, 191, 36, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(239, 68, 68, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 40% 60%, rgba(245, 158, 11, 0.05) 0%, transparent 50%);
    z-index: -1;
}

/* 主標題樣式 */
.brand-story-title {
    position: relative;
    background: linear-gradient(135deg, #dc2626 0%, #ea580c 50%, #d97706 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    /* 為不支援 background-clip 的瀏覽器提供備用顏色 */
    color: #dc2626;
}

.brand-story-title::after {
    content: '';
    position: absolute;
    bottom: -12px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(90deg, #dc2626 0%, #ea580c 50%, #d97706 100%);
    border-radius: 2px;
}

/* 副標題樣式 */
.brand-story-subtitle {
    color: #6b7280;
    font-style: italic;
    position: relative;
}

/* 詩句區塊 */
.brand-story-poem {
    position: relative;
}

.brand-story-poem::before {
    content: '"';
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 4rem;
    color: #fbbf24;
    opacity: 0.3;
    font-family: serif;
    line-height: 1;
}

.brand-story-poem::after {
    content: '"';
    position: absolute;
    bottom: -40px;
    left: 50%;
    transform: translateX(-50%);
    font-size: 4rem;
    color: #fbbf24;
    opacity: 0.3;
    font-family: serif;
    line-height: 1;
}

/* 卡片樣式 */
.brand-story-card {
    position: relative;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.brand-story-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

.brand-story-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    border-radius: inherit;
    z-index: -1;
}

/* 重點強調區塊 */
.brand-story-highlight {
    position: relative;
    overflow: hidden;
}

.brand-story-highlight::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 3s infinite;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* 信念區塊 */
.brand-story-beliefs {
    position: relative;
}

.brand-story-beliefs .space-y-4 > p {
    position: relative;
    padding-left: 2rem;
}

.brand-story-beliefs .space-y-4 > p::before {
    content: '❤️';
    position: absolute;
    left: 0;
    top: 0;
    font-size: 1.2em;
    opacity: 0.8;
}

/* 結語區塊 */
.brand-story-conclusion {
    position: relative;
}

/* 響應式設計 */
@media (max-width: 768px) {
    .brand-story-section {
        padding: 2rem 0;
    }

    .brand-story-title {
        font-size: 2rem;
        line-height: 1.2;
        margin-bottom: 1rem;
    }

    .brand-story-subtitle {
        font-size: 1.25rem;
        margin-bottom: 2rem;
    }

    .brand-story-card {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .brand-story-highlight {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .brand-story-beliefs {
        padding: 1.5rem;
        margin-bottom: 2rem;
    }

    .brand-story-poem {
        margin-bottom: 3rem;
    }

    .brand-story-poem::before,
    .brand-story-poem::after {
        font-size: 2.5rem;
    }

    .brand-story-poem::before {
        top: -10px;
    }

    .brand-story-poem::after {
        bottom: -20px;
    }
}

@media (max-width: 640px) {
    .brand-story-title {
        font-size: 1.75rem;
    }
    
    .brand-story-subtitle {
        font-size: 1.125rem;
    }
    
    .brand-story-card,
    .brand-story-highlight,
    .brand-story-beliefs {
        padding: 1rem;
        margin-bottom: 1.5rem;
    }
    
    .brand-story-beliefs .space-y-4 > p {
        padding-left: 1.5rem;
        font-size: 0.95rem;
    }
}

/* 平板設計優化 */
@media (min-width: 768px) and (max-width: 1024px) {
    .brand-story-card,
    .brand-story-highlight,
    .brand-story-beliefs {
        padding: 2.5rem;
    }
}

/* 大螢幕優化 */
@media (min-width: 1200px) {
    .brand-story-section {
        padding: 6rem 0;
    }
    
    .brand-story-title {
        font-size: 3.5rem;
    }
    
    .brand-story-subtitle {
        font-size: 1.75rem;
    }
}

/* 動畫效果 */
.brand-story-card,
.brand-story-highlight,
.brand-story-beliefs,
.brand-story-conclusion {
    opacity: 0;
    transform: translateY(30px);
    animation: fadeInUp 0.8s ease forwards;
}

.brand-story-content .brand-story-card:nth-of-type(1) { animation-delay: 0.2s; }
.brand-story-content .brand-story-card:nth-of-type(2) { animation-delay: 0.4s; }
.brand-story-content .brand-story-highlight { animation-delay: 0.6s; }
.brand-story-content .brand-story-beliefs { animation-delay: 0.8s; }
.brand-story-content .brand-story-conclusion { animation-delay: 1s; }

@keyframes fadeInUp {
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 印刷友善樣式 */
@media print {
    .brand-story-section {
        background: white !important;
        color: black !important;
    }
    
    .brand-story-card,
    .brand-story-highlight,
    .brand-story-beliefs {
        background: white !important;
        color: black !important;
        box-shadow: none !important;
        border: 1px solid #ccc !important;
    }
    
    .btn-enhanced {
        display: none !important;
    }
}
