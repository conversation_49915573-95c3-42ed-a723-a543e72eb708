function Storage() {
    try {
        return (
            <section id="storage" className="section bg-white" data-name="storage-section">
                <div className="container mx-auto px-4">
                    <h2 className="section-title" data-name="section-title">保存方式說明</h2>

                    {/* 桌面版：三欄並排，行動版：單欄堆疊 */}
                    <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 lg:gap-6 max-w-6xl mx-auto" data-name="storage-content">

                        {/* 冷藏保存方式 */}
                        <div className="bg-gradient-to-br from-blue-50 to-blue-100 p-4 sm:p-5 lg:p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1" data-name="storage-card">
                            <h3 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4 text-blue-800 flex items-center" data-name="storage-title">
                                <i className="fas fa-snowflake mr-2 text-blue-600"></i>
                                冷藏保存方式
                            </h3>
                            <ul className="list-disc pl-6 space-y-2" data-name="storage-list">
                                <li className="leading-relaxed text-gray-700">未拆封可保存6-7天</li>
                                <li className="leading-relaxed text-gray-700">最佳保存溫度：2℃~5℃</li>
                                <li className="leading-relaxed text-gray-700">注意：純米漿製作不可冷凍</li>
                            </ul>
                        </div>

                        {/* 拆封後保存 */}
                        <div className="bg-gradient-to-br from-green-50 to-green-100 p-4 sm:p-5 lg:p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1" data-name="opened-storage-card">
                            <h3 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4 text-green-800 flex items-center" data-name="opened-storage-title">
                                <i className="fas fa-box-open mr-2 text-green-600"></i>
                                拆封後保存
                            </h3>
                            <ul className="list-disc pl-6 space-y-2" data-name="opened-storage-list">
                                <li className="leading-relaxed text-gray-700">建議3天內食用完畢</li>
                                <li className="leading-relaxed text-gray-700">可用乾淨紙巾或布包裹，若有濕氣須及時更換</li>
                            </ul>
                        </div>

                        {/* 品質檢查 */}
                        <div className="bg-gradient-to-br from-amber-50 to-amber-100 p-4 sm:p-5 lg:p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 transform hover:-translate-y-1" data-name="quality-check-card">
                            <h3 className="text-lg sm:text-xl font-bold mb-3 sm:mb-4 text-amber-800 flex items-center" data-name="quality-check-title">
                                <i className="fas fa-search mr-2 text-amber-600"></i>
                                品質檢查
                            </h3>
                            <ul className="list-disc pl-6 space-y-2" data-name="quality-check-list">
                                <li className="leading-relaxed text-gray-700">無黏液且無酸味即為正常</li>
                                <li className="leading-relaxed text-gray-700">避免悶熱及過多濕氣</li>
                                <li className="leading-relaxed text-gray-700">退冰後再冷藏會縮短保存期限</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </section>
        );
    } catch (error) {
        console.error('Storage component error:', error);
        reportError(error);
        return null;
    }
}
