<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>配送提醒彈窗測試</title>
    <script src="https://resource.trickle.so/vendor_lib/unpkg/react@18/umd/react.production.min.js"></script>
    <script src="https://resource.trickle.so/vendor_lib/unpkg/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://resource.trickle.so/vendor_lib/unpkg/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="styles/main.css" rel="stylesheet">
    <link href="styles/delivery-notice-modal.css" rel="stylesheet">
</head>
<body>
    <div id="root"></div>

    <script src="utils/errorUtils.js"></script>
    <script type="text/babel" src="components/DeliveryNoticeModal.js"></script>
    
    <script type="text/babel">
        function TestApp() {
            const [showModal, setShowModal] = React.useState(false);

            const openModal = () => setShowModal(true);
            const closeModal = () => setShowModal(false);

            // 自動顯示彈窗（模擬頁面載入）
            React.useEffect(() => {
                setTimeout(() => {
                    setShowModal(true);
                }, 1000);
            }, []);

            return (
                <div className="min-h-screen bg-gray-100">
                    <div className="container mx-auto py-8">
                        <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
                            配送提醒彈窗測試
                        </h1>
                        
                        <div className="max-w-2xl mx-auto bg-white rounded-lg shadow p-6">
                            <h2 className="text-xl font-semibold mb-4">測試說明：</h2>
                            <ul className="list-disc list-inside space-y-2 text-gray-700 mb-6">
                                <li>彈窗會在頁面載入 1 秒後自動顯示</li>
                                <li>每次重新載入頁面都會顯示彈窗</li>
                                <li>可以點擊背景、ESC 鍵或按鈕關閉彈窗</li>
                                <li>彈窗包含完整的配送提醒內容</li>
                                <li>響應式設計適應不同螢幕尺寸</li>
                            </ul>
                            
                            <div className="text-center">
                                <button 
                                    onClick={openModal}
                                    className="bg-red-500 hover:bg-red-600 text-white px-6 py-3 rounded-lg font-medium transition-colors"
                                >
                                    <i className="fas fa-truck mr-2"></i>
                                    手動開啟配送提醒
                                </button>
                            </div>
                        </div>
                        
                        <div className="mt-8 max-w-2xl mx-auto bg-blue-50 rounded-lg p-6">
                            <h3 className="text-lg font-semibold mb-3 text-blue-800">彈窗內容預覽：</h3>
                            <div className="space-y-3 text-blue-700">
                                <p><strong>1.</strong> 您選擇的到貨日期為「預計送達日期」，實際配送時間將以我們的出貨安排為準。</p>
                                <p><strong>2.</strong> 請您放心外出，無需為了等待蘿蔔糕而待在家中。商品送達前配送人員會提前致電通知...</p>
                                <p><strong>3.</strong> 無論選擇哪種配送方式，我們都採用全程冷藏配送，確保商品新鮮度，請您安心。</p>
                            </div>
                        </div>
                        
                        <div className="mt-8 max-w-2xl mx-auto bg-green-50 rounded-lg p-6">
                            <h3 className="text-lg font-semibold mb-3 text-green-800">測試重點：</h3>
                            <ul className="list-disc list-inside space-y-1 text-green-700">
                                <li>檢查彈窗是否正確顯示</li>
                                <li>測試關閉功能（背景點擊、ESC 鍵、按鈕）</li>
                                <li>確認響應式設計在不同螢幕尺寸下的效果</li>
                                <li>檢查動畫效果是否流暢</li>
                                <li>確認內容完整且易讀</li>
                            </ul>
                        </div>
                    </div>

                    {/* 配送提醒彈窗 */}
                    <DeliveryNoticeModal 
                        isOpen={showModal} 
                        onClose={closeModal} 
                    />
                </div>
            );
        }

        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<TestApp />);
    </script>
</body>
</html>
