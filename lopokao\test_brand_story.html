<!DOCTYPE html>
<html lang="zh-TW">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>品牌故事測試頁面</title>
    <script src="https://resource.trickle.so/vendor_lib/unpkg/react@18/umd/react.production.min.js"></script>
    <script src="https://resource.trickle.so/vendor_lib/unpkg/react-dom@18/umd/react-dom.production.min.js"></script>
    <script src="https://resource.trickle.so/vendor_lib/unpkg/@babel/standalone/babel.min.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <link href="styles/main.css" rel="stylesheet">
    <link href="styles/brand-story.css" rel="stylesheet">
</head>
<body>
    <div id="root"></div>

    <script src="utils/errorUtils.js"></script>
    <script type="text/babel" src="components/BrandStory.js"></script>
    
    <script type="text/babel">
        function TestApp() {
            return (
                <div className="min-h-screen bg-gray-100">
                    <div className="container mx-auto py-8">
                        <h1 className="text-3xl font-bold text-center mb-8 text-gray-800">
                            品牌故事組件測試
                        </h1>
                        <BrandStory />
                    </div>
                </div>
            );
        }

        const root = ReactDOM.createRoot(document.getElementById('root'));
        root.render(<TestApp />);
    </script>
</body>
</html>
