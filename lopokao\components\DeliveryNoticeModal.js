function DeliveryNoticeModal({ isOpen, onClose }) {
    try {
        // 如果彈窗未開啟，不渲染任何內容
        if (!isOpen) return null;

        // 處理背景點擊關閉
        const handleBackdropClick = (e) => {
            if (e.target === e.currentTarget) {
                onClose();
            }
        };

        // 處理 ESC 鍵關閉
        React.useEffect(() => {
            const handleEscKey = (e) => {
                if (e.key === 'Escape') {
                    onClose();
                }
            };

            if (isOpen) {
                document.addEventListener('keydown', handleEscKey);
                // 防止背景滾動
                document.body.style.overflow = 'hidden';
            }

            return () => {
                document.removeEventListener('keydown', handleEscKey);
                document.body.style.overflow = 'unset';
            };
        }, [isOpen, onClose]);

        return (
            <div 
                className="delivery-notice-overlay fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4"
                onClick={handleBackdropClick}
                data-name="delivery-notice-overlay"
            >
                <div 
                    className="delivery-notice-modal bg-white rounded-2xl shadow-2xl max-w-2xl w-full max-h-[90vh] overflow-y-auto"
                    data-name="delivery-notice-modal"
                >
                    {/* 標題區域 */}
                    <div className="delivery-notice-header bg-gradient-to-r from-red-500 to-orange-500 text-white p-4 md:p-5 rounded-t-2xl">
                        <div className="flex items-center justify-between">
                            <div className="flex items-center">
                                <i className="fas fa-truck text-lg md:text-xl mr-2 md:mr-3"></i>
                                <h2 className="text-lg md:text-xl font-bold">配送重要提醒</h2>
                            </div>
                            <button
                                onClick={onClose}
                                className="text-white hover:text-gray-200 transition-colors text-xl md:text-2xl"
                                data-name="close-button"
                            >
                                <i className="fas fa-times"></i>
                            </button>
                        </div>
                    </div>

                    {/* 內容區域 */}
                    <div className="delivery-notice-content p-4 md:p-6">
                        <div className="space-y-3 md:space-y-4">
                            {/* 提醒項目 1 */}
                            <div className="delivery-notice-item flex items-start">
                                <div className="flex-shrink-0 w-7 h-7 bg-red-100 text-red-600 rounded-full flex items-center justify-center font-bold mr-3 mt-0.5 text-sm">
                                    1
                                </div>
                                <div className="flex-1">
                                    <p className="text-gray-700 leading-relaxed text-sm md:text-base">
                                        您選擇的到貨日期為「預計送達日期」，實際配送時間將以我們的出貨安排為準。
                                    </p>
                                </div>
                            </div>

                            {/* 提醒項目 2 */}
                            <div className="delivery-notice-item flex items-start">
                                <div className="flex-shrink-0 w-7 h-7 bg-orange-100 text-orange-600 rounded-full flex items-center justify-center font-bold mr-3 mt-0.5 text-sm">
                                    2
                                </div>
                                <div className="flex-1">
                                    <p className="text-gray-700 leading-relaxed text-sm md:text-base">
                                        請您放心外出，無需為了等待蘿蔔糕而待在家中。商品送達前配送人員會提前致電通知，若您當時不在家中無法收貨，可透過電話請配送人員將商品寄放至您家附近的7-11便利商店，並以冷藏方式為您保存，待您有空時再前往取件即可。
                                    </p>
                                </div>
                            </div>

                            {/* 提醒項目 3 */}
                            <div className="delivery-notice-item flex items-start">
                                <div className="flex-shrink-0 w-7 h-7 bg-green-100 text-green-600 rounded-full flex items-center justify-center font-bold mr-3 mt-0.5 text-sm">
                                    3
                                </div>
                                <div className="flex-1">
                                    <p className="text-gray-700 leading-relaxed text-sm md:text-base">
                                        無論選擇哪種配送方式，我們都採用全程冷藏配送，確保商品新鮮度，請您安心。
                                    </p>
                                </div>
                            </div>
                        </div>

                        {/* 重要提醒框 */}
                        <div className="mt-4 md:mt-6 bg-blue-50 border-l-4 border-blue-400 p-3 rounded-r-lg">
                            <div className="flex items-center">
                                <i className="fas fa-info-circle text-blue-400 text-base mr-2"></i>
                                <p className="text-blue-800 font-medium text-sm md:text-base">
                                    如有任何配送相關問題，歡迎隨時聯繫我們的客服團隊！
                                </p>
                            </div>
                        </div>
                    </div>

                    {/* 按鈕區域 */}
                    <div className="delivery-notice-footer bg-gray-50 px-4 md:px-6 py-3 rounded-b-2xl">
                        <div className="flex flex-col sm:flex-row gap-2 sm:justify-end">
                            <button
                                onClick={onClose}
                                className="btn-enhanced bg-gradient-to-r from-red-500 to-orange-500 text-white px-5 py-2.5 rounded-lg font-medium hover:from-red-600 hover:to-orange-600 transition-all duration-300 flex items-center justify-center text-sm md:text-base"
                                data-name="confirm-button"
                            >
                                <i className="fas fa-check mr-2"></i>
                                我已了解
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        );
    } catch (error) {
        console.error('DeliveryNoticeModal component error:', error);
        reportError(error);
        return null;
    }
}
