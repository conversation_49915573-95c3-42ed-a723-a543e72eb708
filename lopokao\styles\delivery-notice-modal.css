/* 配送提醒彈窗樣式 */

/* 遮罩層 */
.delivery-notice-overlay {
    backdrop-filter: blur(4px);
    animation: fadeIn 0.3s ease-out;
}

/* 彈窗主體 */
.delivery-notice-modal {
    animation: slideInUp 0.4s ease-out;
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* 標題區域 */
.delivery-notice-header {
    position: relative;
    overflow: hidden;
}

.delivery-notice-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    animation: shimmer 2s infinite;
}

/* 內容區域 */
.delivery-notice-content {
    line-height: 1.7;
}

/* 提醒項目 */
.delivery-notice-item {
    padding: 1rem 0;
    border-bottom: 1px solid #f1f5f9;
    transition: all 0.3s ease;
}

.delivery-notice-item:last-child {
    border-bottom: none;
}

.delivery-notice-item:hover {
    background-color: #fafafa;
    border-radius: 8px;
    padding-left: 1rem;
    padding-right: 1rem;
}

/* 編號圓圈動畫 */
.delivery-notice-item .w-8.h-8 {
    transition: all 0.3s ease;
    animation: bounceIn 0.6s ease-out;
}

.delivery-notice-item:hover .w-8.h-8 {
    transform: scale(1.1);
}

/* 按鈕樣式 */
.delivery-notice-footer .btn-enhanced {
    position: relative;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(239, 68, 68, 0.3);
}

.delivery-notice-footer .btn-enhanced::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s ease;
}

.delivery-notice-footer .btn-enhanced:hover::before {
    left: 100%;
}

/* 動畫定義 */
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(30px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes shimmer {
    0% {
        left: -100%;
    }
    100% {
        left: 100%;
    }
}

/* 響應式設計 */
@media (max-width: 640px) {
    .delivery-notice-modal {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
    }
    
    .delivery-notice-header {
        padding: 1.5rem;
    }
    
    .delivery-notice-header h2 {
        font-size: 1.25rem;
    }
    
    .delivery-notice-content {
        padding: 1.5rem;
    }
    
    .delivery-notice-item {
        padding: 0.75rem 0;
    }
    
    .delivery-notice-item .w-8.h-8 {
        width: 1.75rem;
        height: 1.75rem;
        font-size: 0.875rem;
    }
    
    .delivery-notice-footer {
        padding: 1rem 1.5rem;
    }
}

@media (max-width: 480px) {
    .delivery-notice-overlay {
        padding: 0.5rem;
    }
    
    .delivery-notice-modal {
        margin: 0.5rem;
        max-height: calc(100vh - 1rem);
    }
    
    .delivery-notice-header {
        padding: 1rem;
    }
    
    .delivery-notice-content {
        padding: 1rem;
    }
    
    .delivery-notice-footer {
        padding: 1rem;
    }
    
    .delivery-notice-item {
        flex-direction: column;
        align-items: flex-start;
    }
    
    .delivery-notice-item .w-8.h-8 {
        margin-bottom: 0.5rem;
        margin-right: 0;
    }
}

/* 高對比模式支援 */
@media (prefers-contrast: high) {
    .delivery-notice-modal {
        border: 2px solid #000;
    }
    
    .delivery-notice-item {
        border-bottom-color: #666;
    }
}

/* 減少動畫偏好 */
@media (prefers-reduced-motion: reduce) {
    .delivery-notice-overlay,
    .delivery-notice-modal,
    .delivery-notice-item .w-8.h-8,
    .delivery-notice-header::before,
    .delivery-notice-footer .btn-enhanced::before {
        animation: none;
    }
    
    .delivery-notice-item:hover .w-8.h-8 {
        transform: none;
    }
}

/* 深色模式支援 */
@media (prefers-color-scheme: dark) {
    .delivery-notice-modal {
        background-color: #1f2937;
        color: #f9fafb;
    }
    
    .delivery-notice-content {
        color: #e5e7eb;
    }
    
    .delivery-notice-footer {
        background-color: #374151;
    }
    
    .delivery-notice-item:hover {
        background-color: #374151;
    }
}
