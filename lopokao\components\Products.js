function Products() {
    try {
        const products = [
            {
                id: 1,
                name: "古早味蘿蔔糕（純素）",
                price: 250,
                description: "1條約1500克(+-5%)",
                ingredients: "水、在來米、白蘿蔔、鹽、糖、味精、地瓜粉、麵粉",
                image: "https://app.trickle.so/storage/public/images/usr_0f3710f6b8000001/c0593f4e-b5bb-430d-8dca-89a0efeb9aa2.png",
                shippingNote: "購買二條即可免運費"
            },
            {
                id: 2,
                name: "芋頭糕（純素）",
                price: 350,
                description: "1條約1500克(+-5%)",
                ingredients: "水、在來米、芋頭、鹽、糖、味精、麵粉",
                image: "https://app.trickle.so/storage/public/images/usr_0f3710f6b8000001/8a18513c-7432-4bf6-8882-1564f284d2ab.png",
                shippingNote: "購買一條即可免運費"
            },
            {
                id: 3,
                name: "港式蘿蔔糕",
                price: 350,
                description: "1條約1500克(+-5%)",
                ingredients: "火腿肉、自炸豬油、嚴選蝦米、溫體豬肉、100%純胡椒粉、自炸油蔥酥、水、在來米、白蘿蔔、鹽、糖、味精、地瓜粉、麵粉",
                image: "https://app.trickle.so/storage/public/images/usr_0f3710f6b8000001/45bcc7ae-383c-475b-b5d9-fc1c8e4fb2c1.png",
                shippingNote: "購買一條即可免運費"
            }
        ];

        return (
            <section id="products" className="section bg-gray-50" data-name="products-section">
                <div className="container mx-auto px-4">
                    <h2 className="section-title" data-name="section-title">產品介紹</h2>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" data-name="products-grid">
                        {products.map(product => (
                            <ProductCard key={product.id} product={product} />
                        ))}
                    </div>
                </div>
            </section>
        );
    } catch (error) {
        console.error('Products component error:', error);
        reportError(error);
        return null;
    }
}
