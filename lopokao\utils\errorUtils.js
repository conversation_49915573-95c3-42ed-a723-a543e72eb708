/**
 * 錯誤處理工具函數
 * 提供統一的錯誤報告和處理機制
 */

/**
 * 報告錯誤到控制台和可能的錯誤追蹤服務
 * @param {Error|string} error 錯誤對象或錯誤訊息
 * @param {Object} context 額外的上下文資訊
 */
const reportError = (error, context = {}) => {
    try {
        // 基本錯誤資訊
        const errorInfo = {
            message: error?.message || error || 'Unknown error',
            stack: error?.stack || 'No stack trace available',
            timestamp: new Date().toISOString(),
            url: window.location.href,
            userAgent: navigator.userAgent,
            ...context
        };

        // 輸出到控制台
        console.error('🚨 Error Report:', errorInfo);

        // 在開發環境中顯示更詳細的資訊
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            console.group('🔍 Detailed Error Information');
            console.error('Error Object:', error);
            console.error('Context:', context);
            console.error('Stack Trace:', error?.stack);
            console.groupEnd();
        }

        // 可以在這裡加入錯誤追蹤服務的 API 呼叫
        // 例如：Sentry, LogRocket, 或自定義的錯誤收集服務
        
    } catch (reportingError) {
        // 如果錯誤報告本身出錯，至少要記錄原始錯誤
        console.error('Failed to report error:', reportingError);
        console.error('Original error:', error);
    }
};

/**
 * 處理 Promise 拒絕的錯誤
 * @param {Error} error Promise 拒絕的錯誤
 * @param {string} operation 操作描述
 */
const handleAsyncError = (error, operation = 'Unknown operation') => {
    reportError(error, {
        type: 'async_error',
        operation: operation
    });
};

/**
 * 處理組件渲染錯誤
 * @param {Error} error 渲染錯誤
 * @param {string} componentName 組件名稱
 */
const handleComponentError = (error, componentName = 'Unknown component') => {
    reportError(error, {
        type: 'component_error',
        component: componentName
    });
};

/**
 * 處理 API 請求錯誤
 * @param {Error} error API 錯誤
 * @param {string} endpoint API 端點
 * @param {Object} requestData 請求資料
 */
const handleApiError = (error, endpoint = 'Unknown endpoint', requestData = {}) => {
    reportError(error, {
        type: 'api_error',
        endpoint: endpoint,
        requestData: requestData
    });
};

/**
 * 創建錯誤邊界處理器
 * @param {string} componentName 組件名稱
 * @returns {Function} 錯誤處理函數
 */
const createErrorBoundary = (componentName) => {
    return (error, errorInfo) => {
        reportError(error, {
            type: 'error_boundary',
            component: componentName,
            errorInfo: errorInfo
        });
    };
};

/**
 * 全域錯誤處理器設置
 */
const setupGlobalErrorHandlers = () => {
    // 處理未捕獲的 JavaScript 錯誤
    window.addEventListener('error', (event) => {
        reportError(event.error || event.message, {
            type: 'global_error',
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno
        });
    });

    // 處理未處理的 Promise 拒絕
    window.addEventListener('unhandledrejection', (event) => {
        reportError(event.reason, {
            type: 'unhandled_promise_rejection'
        });
    });
};

// 自動設置全域錯誤處理器
if (typeof window !== 'undefined') {
    setupGlobalErrorHandlers();
}

// 將函數掛載到全域物件上，讓其他腳本可以使用
if (typeof window !== 'undefined') {
    window.reportError = reportError;
    window.handleAsyncError = handleAsyncError;
    window.handleComponentError = handleComponentError;
    window.handleApiError = handleApiError;
    window.createErrorBoundary = createErrorBoundary;
}
